import React, { useState } from 'react';
import '../styles/components/DailyTransactions.css';
import DashboardLayout from './DashboardLayout';
import SideNav from './SideNav';
import DashboardModeSelector from './DashboardModeSelector';

// Import icons
import spanImage from '../assets/span.png';
import esvcToken from '../assets/esvc-token.png';

interface DailyTransactionsProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const DailyTransactions: React.FC<DailyTransactionsProps> = () => {
  const [activeTab, setActiveTab] = useState('daily-transactions');
  const [activeTransactionTab, setActiveTransactionTab] = useState('esvc-sold');
  const [timeFilter, setTimeFilter] = useState('all-time');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;



  const transactionTabs = [
    { id: 'esvc-sold', label: 'ESVC Sold' },
    { id: 'solana-purchases', label: 'Solana Purchases' },
    { id: 'btc-transactions', label: 'BTC Transactions' },
    { id: 'usdc-movement', label: 'USDC Movement' }
  ];

  const timeFilters = [
    { id: 'all-time', label: 'All time' },
    { id: 'today', label: 'Today' },
    { id: 'week', label: 'This week' },
    { id: 'month', label: 'This month' }
  ];

  // Mock transaction data
  const transactions = Array.from({ length: 50 }, (_, index) => ({
    id: index + 1,
    type: 'ESVC Sold',
    recipient: 'To 0x6d4...bF1',
    amount: '-400 ESVC',
    time: 'Today, 10:17 AM',
    icon: esvcToken
  }));

  const totalValue = '11,302';



  const totalPages = Math.ceil(transactions.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentTransactions = transactions.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const renderPagination = () => {
    const centerPages = [];
    const maxVisiblePages = 5;

    // Page numbers for center
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage < maxVisiblePages - 1) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      centerPages.push(
        <button
          key={i}
          className={`pagination-btn ${currentPage === i ? 'active' : ''}`}
          onClick={() => handlePageChange(i)}
        >
          {i}
        </button>
      );
    }

    // Add ellipsis and last page if needed
    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        centerPages.push(<span key="ellipsis" className="pagination-ellipsis">...</span>);
      }
      centerPages.push(
        <button
          key={totalPages}
          className={`pagination-btn ${currentPage === totalPages ? 'active' : ''}`}
          onClick={() => handlePageChange(totalPages)}
        >
          {totalPages}
        </button>
      );
    }

    return (
      <>
        {/* Previous button */}
        <button
          className={`pagination-btn ${currentPage === 1 ? 'disabled' : ''}`}
          onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          ← Previous
        </button>

        {/* Center page numbers */}
        <div className="pagination-center">
          {centerPages}
        </div>

        {/* Next button */}
        <button
          className={`pagination-btn ${currentPage === totalPages ? 'disabled' : ''}`}
          onClick={() => currentPage < totalPages && handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          Next →
        </button>
      </>
    );
  };

  return (
    <DashboardLayout className="daily-transactions-container">
      <div className="daily-transactions-content">
        {/* Page Title */}
        <div className="page-header">
          <h1 className="page-title">
            <div className="title-with-span">
              Treasury
              <img src={spanImage} alt="Decorative span" className="title-span" />
            </div>
            Dashboard
          </h1>
          <p className="page-subtitle">
            Live insights into how funds are held, ROI is distributed, and startups are supported.
            Empowering you to stake with trust.
          </p>
        </div>

        <div className="dashboard-layout">
          {/* Dashboard Mode Selector */}
          <div className="dashboard-mode-tabs">
            <DashboardModeSelector />
          </div>

          {/* Dashboard Content Wrapper */}
          <div className="dashboard-content-wrapper">
            {/* Sidebar */}
            <SideNav
              activeTab={activeTab}
              onTabChange={setActiveTab}
            />

            {/* Dashboard Content */}
            <div className="dashboard-content">
            <div className="transactions-header">
              <h2 className="section-title">Daily Transactions</h2>
            </div>

            {/* Transaction Tabs */}
            <div className="transaction-tabs">
              {transactionTabs.map((tab) => (
                <button
                  key={tab.id}
                  className={`transaction-tab ${activeTransactionTab === tab.id ? 'active' : ''}`}
                  onClick={() => setActiveTransactionTab(tab.id)}
                >
                  {tab.label}
                </button>
              ))}
            </div>

            {/* Filters and Total */}
            <div className="transactions-controls">
              <div className="time-filter">
                <select
                  value={timeFilter}
                  onChange={(e) => setTimeFilter(e.target.value)}
                  className="filter-select"
                >
                  {timeFilters.map((filter) => (
                    <option key={filter.id} value={filter.id}>
                      {filter.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="total-value-card">
                <div className="total-label">TOTAL VALUE (BASED ON FILTER DATE)</div>
                <div className="total-amount">{totalValue}</div>
              </div>
            </div>

            {/* Transactions List */}
            <div className="transactions-list">
              {currentTransactions.map((transaction, index) => (
                <div key={transaction.id} className="transaction-item">
                  <div className="transaction-number">{startIndex + index + 1}.</div>
                  <div className="transaction-icon">
                    <img src={transaction.icon} alt="Transaction icon" />
                  </div>
                  <div className="transaction-details">
                    <div className="transaction-type">{transaction.type}</div>
                    <div className="transaction-recipient">{transaction.recipient}</div>
                  </div>
                  <div className="transaction-amount-time">
                    <div className="transaction-amount">{transaction.amount}</div>
                    <div className="transaction-time">{transaction.time}</div>
                  </div>
                </div>
              ))}
            </div>

            {/* Pagination */}
            <div className="pagination">
              {renderPagination()}
            </div>
          </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default DailyTransactions;
