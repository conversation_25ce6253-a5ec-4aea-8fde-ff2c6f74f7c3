import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/components/TradeChallenge.css';
import DashboardLayout from './DashboardLayout';
import vectorAsset from '../assets/Vector.png';


interface TradeChallengeProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const TradeChallenge: React.FC<TradeChallengeProps> = () => {
  const navigate = useNavigate();

  const handleStartTradeChallenge = () => {
    navigate('/trade-challenge-signup');
  };

  return (
    <DashboardLayout className="trade-challenge-container">
      <div className="trade-challenge-content">
        {/* Join ESVC Trade Challenge Section */}
        <div className="join-challenge-section">
          <h1 className="join-challenge-title">Join the ESVC Trade Challenge</h1>

          {/* Progress Steps */}
          <div className="challenge-steps">
            <div className="step-item active">
              <div className="step-circle">1</div>
              <span className="step-label">PAY ONE-TIME FEE</span>
            </div>
            <div className="step-item">
              <div className="step-circle">2</div>
              <span className="step-label">LINK YOUR TRADING ACCOUNT</span>
            </div>
            <div className="step-item">
              <div className="step-circle">3</div>
              <span className="step-label">YOUR TRADING DASHBOARD</span>
            </div>
          </div>

          {/* Payment Section */}
          <div className="payment-section">
            <h2 className="payment-title">Pay Your $100 Entry Fee</h2>
            <p className="payment-subtitle">
              Join the challenge by paying a one-time subscription fee of $100 (USDC equivalent).
              Choose your preferred currency to proceed.
            </p>

            <div className="payment-form">
              <label className="payment-label">Choose your payment currency</label>
              <select className="payment-select">
                <option value="">Click to select</option>
                <option value="usdc">USDC</option>
                <option value="btc">Bitcoin</option>
                <option value="eth">Ethereum</option>
              </select>
            </div>
          </div>
        </div>



        {/* More Than Trading Section */}
        <div className="more-than-trading">
          <div className="more-than-trading-content">
            <h2 className="more-than-trading-title">More Than Trading. Fueling Innovation</h2>
            <p className="more-than-trading-text">
              Your trading journey with us is just the beginning. Stake ESVC to earn daily ROI and unlock the
              chance to pitch your own startup ideas for funding. We reinvest a portion of our platform's profit to
              support bold solutions from our staking community.
            </p>
            <div className="more-than-trading-buttons">
              <button className="start-staking-btn">
                {/* <img src={vectorAsset} alt="Vector" className="button-decoration" /> */}
                Start Staking Now 
                                <img src={vectorAsset} alt="Vector" className="button-decoration" />

              </button>
              <button className="get-funded-btn">Get Funded</button>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default TradeChallenge;
