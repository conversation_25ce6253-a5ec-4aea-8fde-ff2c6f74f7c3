import React, { useState, useEffect } from 'react';
import DashboardLayout from './DashboardLayout';
import Side<PERSON><PERSON> from './SideNav';
import DashboardModeSelector from './DashboardModeSelector';
import {
  <PERSON><PERSON>hart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';

// Import assets
import spanImage from '../assets/span.png';

// Import styles
import '../styles/components/VisualAnalytics.css';

interface VisualAnalyticsProps {}

const VisualAnalytics: React.FC<VisualAnalyticsProps> = () => {
  const [activeTab, setActiveTab] = useState('visual-analytics');
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);
  const [selectedTimeframe, setSelectedTimeframe] = useState('This year');



  // Sample data for Growth of Total Staked Over Time
  const stakingGrowthData = [
    { month: 'Jan', value: 200000 },
    { month: 'Feb', value: 250000 },
    { month: 'Mar', value: 280000 },
    { month: 'Apr', value: 320000 },
    { month: 'May', value: 380000 },
    { month: 'Jun', value: 420000 },
    { month: 'Jul', value: 480000 },
    { month: 'Aug', value: 520000 }
  ];

  // Sample data for ROI Payouts by Tier
  const roiPayoutsData = [
    { tier: '$0-$99', amount: 750000 },
    { tier: '$100-$249', amount: 1200000 },
    { tier: '$250-$499', amount: 950000 },
    { tier: '$500-$999', amount: 1800000 },
    { tier: '$1000-$2499', amount: 1100000 },
    { tier: '$2500-$4999', amount: 1600000 },
    { tier: '$5000-$9999', amount: 2100000 },
    { tier: '$10000-$19999', amount: 1750000 },
    { tier: '$20000-$49999', amount: 1450000 },
    { tier: '$50000+', amount: 1350000 }
  ];

  // Sample data for Token Reserve Distribution
  const tokenDistributionData = [
    { name: 'BTC Holdings', value: 28, amount: 91000, color: '#F7931A' },
    { name: 'Solana Holdings', value: 53, amount: 124000, color: '#00D4AA' },
    { name: 'USDC Holdings', value: 11, amount: 51500, color: '#2775CA' },
    { name: 'ESVC Reserves', value: 8, amount: 51500, color: '#BF4129' }
  ];

  const timeframeOptions = ['This year', 'Last 6 months', 'Last 3 months', 'Last month'];

  return (
    <DashboardLayout className="visual-analytics-container">
      <div className="visual-analytics-content">
        {/* Page Title */}
        <div className="page-header">
          <h1 className="page-title">
            <div className="title-with-span">
              Treasury
              <img src={spanImage} alt="Decorative span" className="title-span" />
            </div>
            Dashboard
          </h1>
          <p className="page-subtitle">
            Live insights into how funds are held, ROI is distributed, and startups are supported.
            Empowering you to stake with trust.
          </p>
        </div>

        <div className="dashboard-layout">
          {/* Dashboard Mode Selector */}
          <div className="dashboard-mode-tabs">
            <DashboardModeSelector />
          </div>

          {/* Dashboard Content Wrapper */}
          <div className="dashboard-content-wrapper">
            {/* Sidebar */}
            <SideNav
              activeTab={activeTab}
              onTabChange={setActiveTab}
            />

            {/* Dashboard Content */}
            <div className="dashboard-content">
            <div className="visual-analytics-header">
              <h2 className="section-title">Visual Analytics</h2>
            </div>

            {/* Charts Container */}
            <div className="charts-container">
              {/* Growth of Total Staked Over Time */}
              <div className="chart-card">
                <div className="chart-header">
                  <h3 className="chart-title">Growth of Total Staked Over Time</h3>
                  <div className="chart-controls">
                    <select
                      value={selectedTimeframe}
                      onChange={(e) => setSelectedTimeframe(e.target.value)}
                      className="timeframe-select"
                    >
                      {timeframeOptions.map((option) => (
                        <option key={option} value={option}>
                          {option}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                <div className="chart-wrapper">
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={stakingGrowthData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                      <XAxis 
                        dataKey="month" 
                        axisLine={false}
                        tickLine={false}
                        tick={{ fill: '#FFFFFF', fontSize: 12 }}
                      />
                      <YAxis 
                        axisLine={false}
                        tickLine={false}
                        tick={{ fill: '#FFFFFF', fontSize: 12 }}
                        tickFormatter={(value) => `$${(value / 1000)}k`}
                      />
                      <Tooltip 
                        contentStyle={{
                          backgroundColor: '#262626',
                          border: '1px solid rgba(255,255,255,0.1)',
                          borderRadius: '8px',
                          color: '#FFFFFF'
                        }}
                        formatter={(value: any) => [`$${(value / 1000)}k`, 'Total Staked']}
                      />
                      <Line 
                        type="monotone" 
                        dataKey="value" 
                        stroke="#00D4AA" 
                        strokeWidth={3}
                        dot={{ fill: '#00D4AA', strokeWidth: 2, r: 4 }}
                        activeDot={{ r: 6, fill: '#00D4AA' }}
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* ROI Payouts by Tier */}
              <div className="chart-card">
                <div className="chart-header">
                  <h3 className="chart-title">ROI Payouts by Tier</h3>
                  <div className="chart-controls">
                    <select
                      value={selectedTimeframe}
                      onChange={(e) => setSelectedTimeframe(e.target.value)}
                      className="timeframe-select"
                    >
                      {timeframeOptions.map((option) => (
                        <option key={option} value={option}>
                          {option}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
                <div className="chart-wrapper">
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={roiPayoutsData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                      <XAxis
                        dataKey="tier"
                        axisLine={false}
                        tickLine={false}
                        tick={{ fill: '#FFFFFF', fontSize: 10 }}
                        angle={-45}
                        textAnchor="end"
                        height={60}
                      />
                      <YAxis
                        axisLine={false}
                        tickLine={false}
                        tick={{ fill: '#FFFFFF', fontSize: 12 }}
                        tickFormatter={(value) => `$${(value / 1000000).toFixed(1)}M`}
                      />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: '#262626',
                          border: '1px solid rgba(255,255,255,0.1)',
                          borderRadius: '8px',
                          color: '#FFFFFF'
                        }}
                        formatter={(value: any) => [`$${(value / 1000000).toFixed(2)}M`, 'ROI Payout']}
                      />
                      <Bar
                        dataKey="amount"
                        fill="#D19049"
                        radius={[4, 4, 0, 0]}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </div>

              {/* Token Reserve Distribution */}
              <div className="chart-card">
                <div className="chart-header">
                  <h3 className="chart-title">Token Reserve Distribution</h3>
                </div>
                <div className="chart-wrapper pie-chart-wrapper">
                  <div className="pie-chart-container">
                    <ResponsiveContainer width="100%" height={isMobile ? 200 : 300}>
                      <PieChart>
                        <Pie
                          data={tokenDistributionData}
                          cx="50%"
                          cy="50%"
                          innerRadius={isMobile ? 40 : 60}
                          outerRadius={isMobile ? 80 : 120}
                          paddingAngle={2}
                          dataKey="value"
                        >
                          {tokenDistributionData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip
                          contentStyle={{
                            backgroundColor: '#262626',
                            border: '1px solid rgba(255,255,255,0.1)',
                            borderRadius: '8px',
                            color: '#FFFFFF'
                          }}
                          formatter={(value: any, _name: any, props: any) => [
                            `${value}%`,
                            props.payload.name
                          ]}
                        />
                      </PieChart>
                    </ResponsiveContainer>
                    <div className="pie-chart-center">
                      <div className="total-holdings">
                        <span className="total-label">Total Holdings</span>
                        <span className="total-value">$318,000.00</span>
                      </div>
                    </div>
                  </div>
                  <div className="pie-chart-legend">
                    {tokenDistributionData.map((item, index) => (
                      <div key={index} className="legend-item">
                        <div className="legend-info">
                          <div
                            className="legend-color"
                            style={{ backgroundColor: item.color }}
                          ></div>
                          <span className="legend-name">{item.name}</span>
                          <div className="legend-details">
                            <span className="legend-percentage">({item.value}%)</span>
                            <span className="legend-amount">${item.amount.toLocaleString()} {item.name.split(' ')[0].toLowerCase()}</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default VisualAnalytics;