/* Live Reserve Content */
.live-reserve-content {
  position: relative;
  z-index: 2;
}

/* Page Header */
.page-header {
  text-align: center;
  padding: 60px 40px 40px;
  max-width: 800px;
  margin: 0 auto;
}

.page-title {
  font-size: 64px;
  font-weight: 600;
  margin: 0 0 24px 0;
  color: #FFFFFF;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  gap: 20px;
}

.title-with-span {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.title-span {
  max-width: 300px;
  height: auto;
}

.page-subtitle {
  font-size: 18px;
  color: #CCCCCC;
  line-height: 28px;
  margin: 0;
}

/* Dashboard Layout */
.dashboard-layout {
  display: flex;
  gap: 40px;
  padding: 0 40px;
  width: 100%;
  margin: 0;
}

/* Sidebar styles removed - using SideNav component */

/* Dashboard Content Wrapper - Borrowed from UserOverview */
.dashboard-content-wrapper {
  display: flex;
  gap: 40px;
  width: 100%;
}

/* Dashboard Content */
.dashboard-content {
  flex: 1;
  max-width: 920px;
}

.live-reserve-header {
  margin-bottom: 32px;
}

.section-title {
  font-size: 32px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0;
}

/* Holdings Grid */
.holdings-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 0;
  margin-top: -42px;
  margin-bottom: 32px;
  width: 100%;
  border-radius: 20px;
  overflow: hidden;
}

.holding-card {
  background: rgba(38, 38, 38, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 0;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  width: 100%;
  box-sizing: border-box;
  border-right: 0.5px solid rgba(255, 255, 255, 0.1);
  border-bottom: 0.5px solid rgba(255, 255, 255, 0.1);
}

/* Remove borders for edge cards */
.holding-card:nth-child(2) {
  border-right: none;
}

.holding-card:nth-child(3) {
  border-bottom: none;
}

.holding-card:nth-child(4) {
  border-right: none;
  border-bottom: none;
}

.holding-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(191, 65, 41, 0.3);
}

.holding-icon-container {
  flex-shrink: 0;
}

.holding-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}



.holding-info {
  flex: 1;
}

.holding-title {
  font-size: 12px;
  font-weight: 600;
  color: #999999;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0 0 8px 0;
}

.holding-value {
  font-size: 28px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 8px 0;
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.holding-unit {
  font-size: 16px;
  font-weight: 500;
  color: #CCCCCC;
}

.holding-change {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 6px;
}

.change-icon {
  width: 14px;
  height: 14px;
  object-fit: contain;
}

.holding-change.positive {
  color: #4CAF50;
}

.holding-change.negative {
  color: #FF6B6B;
}

.holding-change.neutral {
  color: #CCCCCC;
}

/* Total Value Card */
.total-value-card {
  background: rgba(38, 38, 38, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 32px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.total-value-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
  border-color: rgba(191, 65, 41, 0.3);
}

.total-title {
  font-size: 14px;
  font-weight: 600;
  color: #999999;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0 0 16px 0;
}

.total-value {
  font-size: 48px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 12px 0;
}

.total-change {
  font-size: 16px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.total-change.positive {
  color: #4CAF50;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .page-header {
    padding: 40px 20px 30px;
  }

  .page-title {
    font-size: 42px;
    gap: 12px;
    justify-content: center;
  }

  .title-with-span {
    align-items: center;
  }

  .title-span {
    max-width: 180px;
  }

  .page-subtitle {
    font-size: 14px;
  }

  .dashboard-layout {
    flex-direction: column;
    padding: 0 20px;
    gap: 24px;
  }

  .dashboard-content-wrapper {
    flex-direction: column;
    gap: 16px;
  }

  /* Mobile sidebar styles removed - SideNav component handles its own mobile styles */

  .dashboard-content {
    order: 2;
    max-width: 100%;
    width: 100%;
  }

  .section-title {
    font-size: 24px;
  }

  .holdings-grid {
    grid-template-columns: 1fr;
    gap: 0;
    border-radius: 16px;
  }

  .holding-card {
    padding: 20px;
    border-right: none;
    border-bottom: 0.5px solid rgba(255, 255, 255, 0.1);
  }

  .holding-card:last-child {
    border-bottom: none;
  }

  /* Reset desktop border rules for mobile */
  .holding-card:nth-child(2),
  .holding-card:nth-child(3),
  .holding-card:nth-child(4) {
    border-right: none;
    border-bottom: 0.5px solid rgba(255, 255, 255, 0.1);
  }

  .holding-card:nth-child(4) {
    border-bottom: none;
  }

  .holding-value {
    font-size: 24px;
  }

  .total-value {
    font-size: 36px;
  }
}
