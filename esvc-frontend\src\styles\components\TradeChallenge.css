/* Trade Challenge Content */
.trade-challenge-content {
  position: relative;
  z-index: 2;
  padding: 30px 0 80px; /* Reduced top padding to move content up */
}

/* Hero Section */
.hero-section {
  text-align: center;
  padding: 0 40px 80px;
  max-width: 1000px;
  margin: 0 auto;
}

.title-section {
  margin-bottom: 32px;
}

.hero-title {
  font-size: 48px;
  font-weight: 700;
  margin: 0 0 16px 0;
  color: #FFFFFF;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.amount {
  color: #BF4129;
}

.arrow-icon {
  width: 40px;
  height: 40px;
  object-fit: contain;
}

.target {
  color: #FFFFFF;
}

.group-line {
  width: 100%;
  max-width: 600px;
  height: auto;
  margin: -50px auto 0 auto; /* Position directly under title */
  display: block;
}

/* Arrow visibility controls */
.desktop-arrow {
  display: inline-block;
}

.mobile-arrow {
  display: none;
}

.hero-subtitle {
  font-size: 18px;
  color: #CCCCCC;
  line-height: 28px;
  margin: 0 0 40px 0;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.get-bot-btn {
  background: linear-gradient(135deg, #F0C369 0%, #C6741B 100%);
  color: #1A1A1A;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
}

.get-bot-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(240, 195, 105, 0.3);
}

/* Join Challenge Section */
.join-challenge-section {
  text-align: center;
  margin-bottom: 80px;
  padding: 40px 20px;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.join-challenge-title {
  font-size: 32px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 40px 0;
}

/* Challenge Steps */
.challenge-steps {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  margin-bottom: 60px;
  flex-wrap: wrap;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.step-item.active {
  opacity: 1;
}

.step-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #F0C369;
  color: #1A1A1A;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 16px;
}

.step-item:not(.active) .step-circle {
  background: #404040;
  color: #CCCCCC;
}

.step-label {
  font-size: 12px;
  font-weight: 600;
  color: #CCCCCC;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.step-item.active .step-label {
  color: #F0C369;
}

/* Payment Section */
.payment-section {
  max-width: 500px;
  margin: 0 auto;
}

.payment-title {
  font-size: 24px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 16px 0;
}

.payment-subtitle {
  font-size: 16px;
  color: #CCCCCC;
  line-height: 1.5;
  margin: 0 0 32px 0;
}

.payment-form {
  text-align: left;
}

.payment-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #CCCCCC;
  margin-bottom: 8px;
}

.payment-select {
  width: 100%;
  background: #262626;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 12px 16px;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #FFFFFF;
  cursor: pointer;
}

.payment-select:focus {
  outline: none;
  border-color: #F0C369;
}

/* Main Content */
.main-content {
  width: 100%;
  margin: 0;
  padding: 40px 40px 0;
  background: #281705;
}

.main-content > * {
  max-width: 1200px;
  margin: 0 auto;
}

.content-header {
  text-align: center;
  margin-bottom: 60px;
}

.content-title {
  font-size: 32px;
  font-weight: 600;
  color: #F0C369;
  margin: 0 0 16px 0;
}

.content-subtitle {
  font-size: 18px;
  color: #CCCCCC;
  line-height: 28px;
  margin: 0;
}

/* Trading Dashboard */
.trading-dashboard {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-bottom: 60px;
}

/* Live Tracker */
.live-tracker {
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 32px;
}

.tracker-title {
  font-size: 20px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 24px 0;
}

.tracker-stats {
  display: flex;
  flex-direction: column;
  gap: 0; /* Remove gap since we're using padding and borders */
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.stat-icon img {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.stat-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 12px;
  color: #CCCCCC;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 18px;
  color: #FFFFFF;
  font-weight: 700;
}

/* Balance Trend Chart */
.balance-trend {
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 32px;
}

.chart-title {
  font-size: 20px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 24px 0;
}

.chart-container {
  height: 300px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Trading Options */
.trading-options {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 80px; /* Extended spacing to cover button better */
  flex-wrap: wrap;
}

.option-btn {
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 50px;
  padding: 16px 24px;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.option-btn:hover {
  border-color: #F0C369;
  color: #FFFFFF;
}

.option-btn.active {
  background: rgba(240, 195, 105, 0.1);
  border-color: #F0C369;
  color: #F0C369;
}

.option-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}

.option-icon img {
  width: 20px;
  height: 20px;
  object-fit: contain;
}

/* Bottom CTA */
.bottom-cta {
  text-align: center;
}

.get-bot-btn-bottom {
  background: linear-gradient(135deg, #F0C369 0%, #C6741B 100%);
  color: #1A1A1A;
  border: none;
  border-radius: 12px;
  padding: 20px 40px;
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
}

.get-bot-btn-bottom:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(240, 195, 105, 0.3);
}

/* How it Works Section */
.how-it-works {
  padding: 80px 40px;
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: start;
}

.how-it-works-left {
  display: flex;
  flex-direction: column;
}

.how-it-works-title {
  font-size: 32px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  gap: 16px;
}

.how-it-works-subtitle {
  font-size: 16px;
  color: #CCCCCC;
  margin: 0 0 40px 0;
  line-height: 24px;
}

.steps-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.step-item {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.step-number {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 14px;
  font-weight: 600;
  color: #F0C369;
  min-width: 40px;
  text-align: center;
}

.step-text {
  font-size: 16px;
  color: #FFFFFF;
  font-weight: 500;
}

.how-it-works-right {
  display: flex;
  flex-direction: column;
}

.backed-section {
  background: rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 40px;
}

.backed-title {
  font-size: 24px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 8px 0;
}

.backed-subtitle {
  font-size: 16px;
  color: #BF4129;
  margin: 0 0 24px 0;
  font-weight: 600;
}

.backed-list {
  list-style: none;
  padding: 0;
  margin: 0 0 24px 0;
}

.backed-list li {
  font-size: 14px;
  color: #CCCCCC;
  margin-bottom: 8px;
  padding-left: 16px;
  position: relative;
  line-height: 20px;
}

.backed-list li::before {
  content: "•";
  color: #F0C369;
  position: absolute;
  left: 0;
}

.treasury-link {
  color: #F0C369;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.treasury-link:hover {
  text-decoration: underline;
}

.join-movement {
  text-align: center;
  margin-top: 40px;
}

.join-movement-text {
  font-size: 16px;
  color: #CCCCCC;
  margin: 0 0 24px 0;
}

.get-bot-btn-section {
  background: linear-gradient(135deg, #F0C369 0%, #C6741B 100%);
  color: #1A1A1A;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
}

.get-bot-btn-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(240, 195, 105, 0.3);
}

/* More Than Trading Section */
.more-than-trading {
  background: rgba(0, 0, 0, 0.6);
  padding: 80px 40px;
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.more-than-trading-content {
  max-width: 800px;
  margin: 0 auto;
}

.more-than-trading-title {
  font-size: 32px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 24px 0;
}

.more-than-trading-text {
  font-size: 16px;
  color: #CCCCCC;
  line-height: 24px;
  margin: 0 0 40px 0;
}

.more-than-trading-buttons {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

.start-staking-btn {
  background: #BF4129;
  color: #FFFFFF;
  border: none;
  border-radius: 12px;
  padding: 16px 32px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.start-staking-btn:hover {
  background: #A03622;
  transform: translateY(-2px);
}

.get-funded-btn {
  background: transparent;
  color: #FFFFFF;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 12px;
  padding: 16px 32px;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.get-funded-btn:hover {
  border-color: #F0C369;
  color: #F0C369;
  transform: translateY(-2px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .trade-challenge-content {
    padding: 60px 0 60px; /* Reduced top padding for mobile */
  }

  .hero-section {
    padding: 0 20px 60px;
  }

  .title-section {
    margin-bottom: 24px;
  }

  .hero-title {
    font-size: 32px;
    flex-direction: column;
    gap: 12px;
    text-align: center;
    margin-bottom: 16px;
  }

  /* Mobile arrow controls */
  .desktop-arrow {
    display: none;
  }

  .mobile-arrow {
    display: inline-block;
  }

  .arrow-icon {
    width: 28px;
    height: 28px;
  }

  .group-line {
    max-width: 400px;
    margin: 16px auto 0 auto; /* Position directly under "1 Billion in 3,000 Trades" on mobile */
  }

  .hero-subtitle {
    font-size: 16px;
  }

  .main-content {
    padding: 0 20px;
  }

  .content-header {
    margin-bottom: 60px;
    padding-top: 60px; /* Add space to the top on mobile */
  }

  .content-title {
    font-size: 24px;
  }

  .content-subtitle {
    font-size: 16px;
  }

  .trading-dashboard {
    grid-template-columns: 1fr;
    gap: 24px;
    margin-bottom: 40px;
  }

  .live-tracker,
  .balance-trend {
    padding: 24px;
  }

  .tracker-stats {
    flex-direction: column;
    gap: 0; /* Remove gap since we're using padding and borders */
  }

  .tracker-title,
  .chart-title {
    font-size: 18px;
  }

  .stat-item {
    gap: 12px;
  }

  /* Join Challenge Section - Mobile */
  .join-challenge-section {
    margin-bottom: 40px;
    padding: 24px 16px;
    border-radius: 16px;
  }

  .join-challenge-title {
    font-size: 24px;
    margin-bottom: 32px;
  }

  .challenge-steps {
    gap: 24px;
    margin-bottom: 40px;
  }

  .step-circle {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .step-label {
    font-size: 10px;
    text-align: center;
  }

  .payment-title {
    font-size: 20px;
  }

  .payment-subtitle {
    font-size: 14px;
    margin-bottom: 24px;
  }

  .stat-icon {
    width: 32px;
    height: 32px;
    font-size: 18px;
  }

  .stat-value {
    font-size: 16px;
  }

  .chart-placeholder {
    height: 200px;
  }

  .trading-options {
    flex-direction: column;
    align-items: center;
    gap: 12px;
    margin-bottom: 40px; /* Reduced spacing */
  }

  .option-btn {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }

  .bottom-cta {
    padding: 20px 0; /* Reduced space around button on mobile */
  }

  .get-bot-btn-bottom {
    padding: 16px 32px;
    font-size: 16px;
  }

  /* How it Works Mobile */
  .how-it-works {
    grid-template-columns: 1fr;
    gap: 40px;
    padding: 60px 20px;
  }

  .how-it-works-title {
    font-size: 24px;
  }

  .how-it-works-subtitle {
    font-size: 14px;
  }

  .step-item {
    padding: 16px;
  }

  .step-text {
    font-size: 14px;
  }

  .backed-section {
    padding: 24px;
    margin-bottom: 24px;
  }

  .backed-title {
    font-size: 20px;
  }

  .backed-subtitle {
    font-size: 14px;
  }

  .backed-list li {
    font-size: 13px;
  }

  .treasury-link {
    font-size: 13px;
  }

  .join-movement {
    margin-top: 24px;
  }

  .join-movement-text {
    font-size: 14px;
  }

  .get-bot-btn-section {
    padding: 14px 28px;
    font-size: 14px;
  }

  /* More Than Trading Mobile */
  .more-than-trading {
    padding: 60px 20px;
  }

  .more-than-trading-title {
    font-size: 24px;
  }

  .more-than-trading-text {
    font-size: 14px;
  }

  .more-than-trading-buttons {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }

  .start-staking-btn,
  .get-funded-btn {
    width: 100%;
    max-width: 300px;
    padding: 14px 28px;
    font-size: 14px;
    justify-content: center;
  }
}
